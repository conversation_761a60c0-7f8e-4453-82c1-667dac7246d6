#app {
    height: 100%;
}
.fantastic-admin-home {
    position: absolute;
    z-index: 10000;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    color: #736477;
    background-color: snow;
}
.fantastic-admin-home .loading {
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}
.fantastic-admin-home .square {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 20px;
    width: 20px;
}
.fantastic-admin-home .square::before {
    content: "";
    width: 10px;
    height: 10px;
    border-radius: 15%;
    border: 3px solid #8c858f;
}
.fantastic-admin-home .loading.animate .square::before {
    animation: square-to-dot-animation 1.2s linear;
}
.fantastic-admin-home .loading.animate .square:nth-child(1)::before {
    animation-delay: calc(100ms * 1);
}
.fantastic-admin-home .loading.animate .square:nth-child(2)::before {
    animation-delay: calc(100ms * 2);
}
.fantastic-admin-home .loading.animate .square:nth-child(3)::before {
    animation-delay: calc(100ms * 3);
}
.fantastic-admin-home .loading.animate .square:nth-child(4)::before {
    animation-delay: calc(100ms * 4);
}
@keyframes square-to-dot-animation {
    15%,
    55% {
        border-radius: 100%;
        width: 0;
        height: 0;
        margin: 5px;
        border-width: 5px;
    }
    70% {
        border-radius: 15%;
        width: 10px;
        height: 10px;
        margin: initial;
        border-width: 3px;
    }
}
.fantastic-admin-home .text {
    font-size: 24px;
    margin-top: 20px;
}
#browser-upgrade {
    position: absolute;
    z-index: 10001;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    display: none;
    user-select: none;
    color: #736477;
    background-color: snow;
}
#browser-upgrade .title {
    margin: 40px 0;
    text-align: center;
    font-size: 24px;
}
#browser-upgrade .browsers {
    text-align: center;
}
#browser-upgrade .browsers .browser {
    display: inline-block;
    margin: 0 20px;
    cursor: pointer;
    text-decoration: none;
}
#browser-upgrade .browsers .browser .browser-icon {
    display: block;
    width: 50px;
    height: 50px;
    margin: 0 auto;
    border: none;
}
#browser-upgrade .browsers .browser .browser-name {
    text-align: center;
    margin-top: 10px;
    padding-bottom: 2px;
    color: #736477;
    border-bottom: 1px solid transparent;
}
#browser-upgrade .browsers .browser:hover .browser-name {
    border-bottom: 1px solid #736477;
}
